<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('employees', function (Blueprint $table) {
            $table->id();
            $table->string('employee_id')->unique();
            $table->string('first_name');
            $table->string('last_name');
            $table->string('email')->unique();
            $table->string('phone')->nullable();
            $table->foreignId('institution_id')->constrained()->onDelete('cascade');
            $table->decimal('salary', 12, 2);
            $table->decimal('expected_monthly_savings', 10, 2);
            $table->enum('savings_type', ['fixed', 'percentage'])->default('percentage');
            $table->decimal('savings_rate', 5, 2)->nullable(); // For percentage-based savings
            $table->date('employment_date');
            $table->date('enrollment_date');
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('employees');
    }
};
