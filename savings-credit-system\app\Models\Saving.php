<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Saving extends Model
{
    use HasFactory;

    protected $fillable = [
        'employee_id',
        'expected_amount',
        'actual_amount',
        'due_date',
        'payment_date',
        'status',
        'admin_notes',
        'approved_by',
        'approved_at',
    ];

    protected $casts = [
        'expected_amount' => 'decimal:2',
        'actual_amount' => 'decimal:2',
        'due_date' => 'date',
        'payment_date' => 'date',
        'approved_at' => 'datetime',
    ];

    /**
     * Get the employee that owns the saving.
     */
    public function employee(): BelongsTo
    {
        return $this->belongsTo(Employee::class);
    }

    /**
     * Get the admin who approved the saving.
     */
    public function approvedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    /**
     * Get the payment proofs for the saving.
     */
    public function paymentProofs(): HasMany
    {
        return $this->hasMany(PaymentProof::class);
    }

    /**
     * Check if saving is overdue.
     */
    public function getIsOverdueAttribute(): bool
    {
        return $this->status === 'pending' && $this->due_date < now();
    }

    /**
     * Get days overdue.
     */
    public function getDaysOverdueAttribute(): int
    {
        if (!$this->is_overdue) {
            return 0;
        }
        
        return now()->diffInDays($this->due_date);
    }

    /**
     * Scope a query to only include pending savings.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope a query to only include approved savings.
     */
    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    /**
     * Scope a query to only include overdue savings.
     */
    public function scopeOverdue($query)
    {
        return $query->where('status', 'pending')
                    ->where('due_date', '<', now());
    }

    /**
     * Scope a query for a specific month.
     */
    public function scopeForMonth($query, $year, $month)
    {
        return $query->whereYear('due_date', $year)
                    ->whereMonth('due_date', $month);
    }

    /**
     * Approve the saving.
     */
    public function approve(User $admin, ?string $notes = null): bool
    {
        $this->update([
            'status' => 'approved',
            'approved_by' => $admin->id,
            'approved_at' => now(),
            'admin_notes' => $notes,
            'payment_date' => now(),
        ]);

        return true;
    }

    /**
     * Reject the saving.
     */
    public function reject(User $admin, string $reason): bool
    {
        $this->update([
            'status' => 'rejected',
            'approved_by' => $admin->id,
            'approved_at' => now(),
            'admin_notes' => $reason,
        ]);

        return true;
    }
}
