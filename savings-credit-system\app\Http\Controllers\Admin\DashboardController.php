<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Employee;
use App\Models\Institution;
use App\Models\Loan;
use App\Models\Saving;
use Illuminate\Http\Request;
use Illuminate\View\View;

class DashboardController extends Controller
{
    /**
     * Display the admin dashboard.
     */
    public function index(): View
    {
        // Key Performance Indicators
        $totalEmployees = Employee::active()->count();
        $totalInstitutions = Institution::active()->count();
        $totalSavings = Saving::approved()->sum('actual_amount');
        $totalLoans = Loan::disbursed()->sum('approved_amount');
        $activeSavers = Employee::whereHas('savings', function ($query) {
            $query->where('status', 'approved')
                  ->where('due_date', '>=', now()->subMonths(3));
        })->count();

        // Pending approvals
        $pendingSavings = Saving::pending()->count();
        $pendingLoans = Loan::pending()->count();
        $overduePayments = Loan::whereHas('repayments', function ($query) {
            $query->where('status', 'pending')
                  ->where('due_date', '<', now());
        })->count();

        // Recent activities
        $recentSavings = Saving::with(['employee.institution'])
            ->latest()
            ->take(10)
            ->get();

        $recentLoans = Loan::with(['employee.institution'])
            ->latest()
            ->take(10)
            ->get();

        // Monthly savings trend (last 6 months)
        $savingsTrend = [];
        for ($i = 5; $i >= 0; $i--) {
            $date = now()->subMonths($i);
            $month = $date->format('M Y');
            $amount = Saving::approved()
                ->whereYear('payment_date', $date->year)
                ->whereMonth('payment_date', $date->month)
                ->sum('actual_amount');
            $savingsTrend[$month] = $amount;
        }

        // Institution performance
        $institutionStats = Institution::withCount(['employees', 'activeEmployees'])
            ->with(['employees' => function ($query) {
                $query->withSum(['savings as total_savings' => function ($query) {
                    $query->where('status', 'approved');
                }], 'actual_amount');
            }])
            ->get()
            ->map(function ($institution) {
                return [
                    'name' => $institution->name,
                    'employees_count' => $institution->employees_count,
                    'active_employees_count' => $institution->active_employees_count,
                    'total_savings' => $institution->employees->sum('total_savings') ?? 0,
                ];
            });

        return view('admin.dashboard', compact(
            'totalEmployees',
            'totalInstitutions',
            'totalSavings',
            'totalLoans',
            'activeSavers',
            'pendingSavings',
            'pendingLoans',
            'overduePayments',
            'recentSavings',
            'recentLoans',
            'savingsTrend',
            'institutionStats'
        ));
    }

    /**
     * Get dashboard statistics for AJAX requests.
     */
    public function getStats(Request $request)
    {
        $period = $request->get('period', 'month'); // month, quarter, year

        $startDate = match($period) {
            'quarter' => now()->subMonths(3),
            'year' => now()->subYear(),
            default => now()->subMonth(),
        };

        $stats = [
            'total_savings' => Saving::approved()
                ->where('payment_date', '>=', $startDate)
                ->sum('actual_amount'),
            'total_loans' => Loan::where('disbursement_date', '>=', $startDate)
                ->sum('approved_amount'),
            'new_employees' => Employee::where('enrollment_date', '>=', $startDate)
                ->count(),
            'loan_repayments' => Loan::whereHas('repayments', function ($query) use ($startDate) {
                $query->where('payment_date', '>=', $startDate)
                      ->where('status', 'paid');
            })->count(),
        ];

        return response()->json($stats);
    }

    /**
     * Export dashboard data.
     */
    public function export(Request $request)
    {
        $type = $request->get('type', 'summary'); // summary, detailed

        // This would typically use a package like Laravel Excel
        // For now, return a simple CSV response
        
        $filename = "dashboard_export_" . now()->format('Y_m_d_H_i_s') . ".csv";
        
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"$filename\"",
        ];

        $callback = function() use ($type) {
            $file = fopen('php://output', 'w');
            
            if ($type === 'summary') {
                fputcsv($file, ['Metric', 'Value']);
                fputcsv($file, ['Total Employees', Employee::active()->count()]);
                fputcsv($file, ['Total Institutions', Institution::active()->count()]);
                fputcsv($file, ['Total Savings', Saving::approved()->sum('actual_amount')]);
                fputcsv($file, ['Total Loans', Loan::disbursed()->sum('approved_amount')]);
                fputcsv($file, ['Pending Savings', Saving::pending()->count()]);
                fputcsv($file, ['Pending Loans', Loan::pending()->count()]);
            } else {
                // Detailed export would include more comprehensive data
                fputcsv($file, ['Employee ID', 'Name', 'Institution', 'Total Savings', 'Active Loans']);
                
                Employee::with(['institution', 'savings', 'loans'])
                    ->chunk(100, function ($employees) use ($file) {
                        foreach ($employees as $employee) {
                            fputcsv($file, [
                                $employee->employee_id,
                                $employee->full_name,
                                $employee->institution->name,
                                $employee->total_savings,
                                $employee->active_loans->count(),
                            ]);
                        }
                    });
            }
            
            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
