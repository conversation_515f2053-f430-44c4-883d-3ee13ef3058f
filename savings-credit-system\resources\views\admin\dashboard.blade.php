@extends('layouts.app')

@section('content')
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">Admin Dashboard</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary">
                <i class="bi bi-download"></i> Export
            </button>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stat-card">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Total Employees</div>
                        <div class="h5 mb-0 font-weight-bold">{{ number_format($totalEmployees) }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-people fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stat-card success">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Total Savings</div>
                        <div class="h5 mb-0 font-weight-bold">${{ number_format($totalSavings, 2) }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-piggy-bank fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stat-card warning">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Total Loans</div>
                        <div class="h5 mb-0 font-weight-bold">${{ number_format($totalLoans, 2) }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-cash-stack fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stat-card info">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Institutions</div>
                        <div class="h5 mb-0 font-weight-bold">{{ number_format($totalInstitutions) }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-building fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Pending Approvals -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card">
            <div class="card-header bg-warning text-dark">
                <h6 class="m-0 font-weight-bold">
                    <i class="bi bi-clock"></i> Pending Savings
                </h6>
            </div>
            <div class="card-body text-center">
                <div class="h3 text-warning">{{ $pendingSavings }}</div>
                <a href="{{ route('admin.savings.index', ['status' => 'pending']) }}" class="btn btn-warning btn-sm">
                    Review Now
                </a>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h6 class="m-0 font-weight-bold">
                    <i class="bi bi-clock"></i> Pending Loans
                </h6>
            </div>
            <div class="card-body text-center">
                <div class="h3 text-info">{{ $pendingLoans }}</div>
                <a href="#" class="btn btn-info btn-sm">
                    Review Now
                </a>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-header bg-success text-white">
                <h6 class="m-0 font-weight-bold">
                    <i class="bi bi-check-circle"></i> Quick Actions
                </h6>
            </div>
            <div class="card-body">
                <a href="{{ route('admin.employees.create') }}" class="btn btn-primary btn-sm d-block mb-2">
                    <i class="bi bi-plus"></i> Add Employee
                </a>
                <button class="btn btn-success btn-sm d-block" data-bs-toggle="modal" data-bs-target="#generateSavingsModal">
                    <i class="bi bi-calendar-plus"></i> Generate Monthly Savings
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activities -->
<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold">
                    <i class="bi bi-clock-history"></i> Recent Savings
                </h6>
            </div>
            <div class="card-body">
                @if($recentSavings->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Employee</th>
                                    <th>Amount</th>
                                    <th>Status</th>
                                    <th>Date</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($recentSavings as $saving)
                                <tr>
                                    <td>{{ $saving->employee->full_name }}</td>
                                    <td>${{ number_format($saving->expected_amount, 2) }}</td>
                                    <td>
                                        <span class="badge bg-{{ $saving->status === 'approved' ? 'success' : ($saving->status === 'rejected' ? 'danger' : 'warning') }}">
                                            {{ ucfirst($saving->status) }}
                                        </span>
                                    </td>
                                    <td>{{ $saving->due_date->format('M d') }}</td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <p class="text-muted">No recent savings found.</p>
                @endif
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold">
                    <i class="bi bi-clock-history"></i> Recent Loans
                </h6>
            </div>
            <div class="card-body">
                @if($recentLoans->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Employee</th>
                                    <th>Amount</th>
                                    <th>Status</th>
                                    <th>Date</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($recentLoans as $loan)
                                <tr>
                                    <td>{{ $loan->employee->full_name }}</td>
                                    <td>${{ number_format($loan->requested_amount, 2) }}</td>
                                    <td>
                                        <span class="badge bg-{{ $loan->status === 'approved' ? 'success' : ($loan->status === 'rejected' ? 'danger' : 'warning') }}">
                                            {{ ucfirst($loan->status) }}
                                        </span>
                                    </td>
                                    <td>{{ $loan->application_date->format('M d') }}</td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <p class="text-muted">No recent loans found.</p>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- Generate Monthly Savings Modal -->
<div class="modal fade" id="generateSavingsModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form action="{{ route('admin.savings.generate-monthly') }}" method="POST">
                @csrf
                <div class="modal-header">
                    <h5 class="modal-title">Generate Monthly Savings</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="month" class="form-label">Select Month</label>
                        <input type="month" class="form-control" id="month" name="month" 
                               value="{{ now()->format('Y-m') }}" required>
                    </div>
                    <p class="text-muted">
                        This will create savings records for all active employees for the selected month.
                    </p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Generate</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection
