<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Loan extends Model
{
    use HasFactory;

    protected $fillable = [
        'loan_number',
        'employee_id',
        'requested_amount',
        'approved_amount',
        'interest_rate',
        'duration_months',
        'monthly_payment',
        'total_amount',
        'application_date',
        'approval_date',
        'disbursement_date',
        'status',
        'purpose',
        'admin_notes',
        'rejection_reason',
        'approved_by',
        'approved_at',
    ];

    protected $casts = [
        'requested_amount' => 'decimal:2',
        'approved_amount' => 'decimal:2',
        'interest_rate' => 'decimal:2',
        'monthly_payment' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'application_date' => 'date',
        'approval_date' => 'date',
        'disbursement_date' => 'date',
        'approved_at' => 'datetime',
    ];

    /**
     * Get the employee that owns the loan.
     */
    public function employee(): BelongsTo
    {
        return $this->belongsTo(Employee::class);
    }

    /**
     * Get the admin who approved the loan.
     */
    public function approvedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    /**
     * Get the repayments for the loan.
     */
    public function repayments(): HasMany
    {
        return $this->hasMany(LoanRepayment::class);
    }

    /**
     * Calculate loan details when approved.
     */
    public function calculateLoanDetails(): void
    {
        if (!$this->approved_amount || !$this->interest_rate || !$this->duration_months) {
            return;
        }

        $principal = $this->approved_amount;
        $rate = $this->interest_rate / 100 / 12; // Monthly interest rate
        $months = $this->duration_months;

        // Calculate monthly payment using loan formula
        if ($rate > 0) {
            $monthlyPayment = $principal * ($rate * pow(1 + $rate, $months)) / (pow(1 + $rate, $months) - 1);
        } else {
            $monthlyPayment = $principal / $months;
        }

        $this->monthly_payment = round($monthlyPayment, 2);
        $this->total_amount = round($monthlyPayment * $months, 2);
    }

    /**
     * Generate repayment schedule.
     */
    public function generateRepaymentSchedule(): void
    {
        if (!$this->disbursement_date || !$this->monthly_payment) {
            return;
        }

        // Delete existing repayments
        $this->repayments()->delete();

        $dueDate = $this->disbursement_date->copy()->addMonth();

        for ($i = 1; $i <= $this->duration_months; $i++) {
            LoanRepayment::create([
                'loan_id' => $this->id,
                'installment_number' => $i,
                'expected_amount' => $this->monthly_payment,
                'due_date' => $dueDate->copy(),
                'status' => 'pending',
            ]);

            $dueDate->addMonth();
        }
    }

    /**
     * Get remaining balance.
     */
    public function getRemainingBalanceAttribute(): float
    {
        $paidAmount = $this->repayments()
            ->where('status', 'paid')
            ->sum('paid_amount');

        return $this->total_amount - $paidAmount;
    }

    /**
     * Get next due payment.
     */
    public function getNextDuePaymentAttribute(): ?LoanRepayment
    {
        return $this->repayments()
            ->where('status', 'pending')
            ->orderBy('due_date')
            ->first();
    }

    /**
     * Get overdue payments.
     */
    public function getOverduePaymentsAttribute()
    {
        return $this->repayments()
            ->where('status', 'pending')
            ->where('due_date', '<', now())
            ->get();
    }

    /**
     * Check if loan is overdue.
     */
    public function getIsOverdueAttribute(): bool
    {
        return $this->overdue_payments->count() > 0;
    }

    /**
     * Approve the loan.
     */
    public function approve(User $admin, float $approvedAmount, float $interestRate, int $durationMonths, ?string $notes = null): bool
    {
        $this->update([
            'approved_amount' => $approvedAmount,
            'interest_rate' => $interestRate,
            'duration_months' => $durationMonths,
            'status' => 'approved',
            'approved_by' => $admin->id,
            'approved_at' => now(),
            'approval_date' => now(),
            'admin_notes' => $notes,
        ]);

        $this->calculateLoanDetails();
        $this->save();

        return true;
    }

    /**
     * Reject the loan.
     */
    public function reject(User $admin, string $reason): bool
    {
        $this->update([
            'status' => 'rejected',
            'approved_by' => $admin->id,
            'approved_at' => now(),
            'rejection_reason' => $reason,
        ]);

        return true;
    }

    /**
     * Disburse the loan.
     */
    public function disburse(): bool
    {
        if ($this->status !== 'approved') {
            return false;
        }

        $this->update([
            'status' => 'disbursed',
            'disbursement_date' => now(),
        ]);

        $this->generateRepaymentSchedule();

        return true;
    }

    /**
     * Generate unique loan number.
     */
    public static function generateLoanNumber(): string
    {
        $year = date('Y');
        $month = date('m');
        $count = static::whereYear('created_at', $year)
                      ->whereMonth('created_at', $month)
                      ->count() + 1;

        return sprintf('LN%s%s%04d', $year, $month, $count);
    }

    /**
     * Boot method to auto-generate loan number.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($loan) {
            if (!$loan->loan_number) {
                $loan->loan_number = static::generateLoanNumber();
            }
        });
    }

    /**
     * Scope queries for different statuses.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    public function scopeDisbursed($query)
    {
        return $query->where('status', 'disbursed');
    }

    public function scopeActive($query)
    {
        return $query->whereIn('status', ['approved', 'disbursed']);
    }
}
