<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Saving;
use App\Models\Employee;
use App\Models\Institution;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;

class SavingsController extends Controller
{
    /**
     * Display a listing of savings.
     */
    public function index(Request $request): View
    {
        $query = Saving::with(['employee.institution', 'paymentProofs']);

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by institution
        if ($request->filled('institution_id')) {
            $query->whereHas('employee', function ($q) use ($request) {
                $q->where('institution_id', $request->institution_id);
            });
        }

        // Filter by date range
        if ($request->filled('date_from')) {
            $query->where('due_date', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->where('due_date', '<=', $request->date_to);
        }

        // Search by employee
        if ($request->filled('search')) {
            $search = $request->search;
            $query->whereHas('employee', function ($q) use ($search) {
                $q->where('first_name', 'like', "%{$search}%")
                  ->orWhere('last_name', 'like', "%{$search}%")
                  ->orWhere('employee_id', 'like', "%{$search}%");
            });
        }

        $savings = $query->latest()->paginate(20);
        $institutions = Institution::active()->get();

        return view('admin.savings.index', compact('savings', 'institutions'));
    }

    /**
     * Display the specified saving for review.
     */
    public function show(Saving $saving): View
    {
        $saving->load(['employee.institution', 'paymentProofs', 'approvedBy']);
        return view('admin.savings.show', compact('saving'));
    }

    /**
     * Approve a saving.
     */
    public function approve(Request $request, Saving $saving): RedirectResponse
    {
        $request->validate([
            'admin_notes' => 'nullable|string|max:1000',
        ]);

        $saving->approve(auth()->user(), $request->admin_notes);

        return redirect()->route('admin.savings.index')
            ->with('success', 'Saving approved successfully.');
    }

    /**
     * Reject a saving.
     */
    public function reject(Request $request, Saving $saving): RedirectResponse
    {
        $request->validate([
            'admin_notes' => 'required|string|max:1000',
        ]);

        $saving->reject(auth()->user(), $request->admin_notes);

        return redirect()->route('admin.savings.index')
            ->with('success', 'Saving rejected successfully.');
    }

    /**
     * Bulk approve savings.
     */
    public function bulkApprove(Request $request): RedirectResponse
    {
        $request->validate([
            'saving_ids' => 'required|array',
            'saving_ids.*' => 'exists:savings,id',
        ]);

        $approved = 0;
        foreach ($request->saving_ids as $savingId) {
            $saving = Saving::find($savingId);
            if ($saving && $saving->status === 'pending') {
                $saving->approve(auth()->user());
                $approved++;
            }
        }

        return redirect()->route('admin.savings.index')
            ->with('success', "Approved {$approved} savings successfully.");
    }

    /**
     * Generate monthly savings for all employees.
     */
    public function generateMonthlySavings(Request $request): RedirectResponse
    {
        $request->validate([
            'month' => 'required|date_format:Y-m',
        ]);

        $month = $request->month;
        $date = \Carbon\Carbon::createFromFormat('Y-m', $month);
        $dueDate = $date->endOfMonth();

        $employees = Employee::active()->get();
        $generated = 0;

        foreach ($employees as $employee) {
            // Check if savings already exists for this month
            $exists = Saving::where('employee_id', $employee->id)
                ->whereYear('due_date', $date->year)
                ->whereMonth('due_date', $date->month)
                ->exists();

            if (!$exists) {
                Saving::create([
                    'employee_id' => $employee->id,
                    'expected_amount' => $employee->calculateExpectedSavings(),
                    'due_date' => $dueDate,
                    'status' => 'pending',
                ]);
                $generated++;
            }
        }

        return redirect()->route('admin.savings.index')
            ->with('success', "Generated {$generated} monthly savings records.");
    }

    /**
     * Export savings data.
     */
    public function export(Request $request)
    {
        $query = Saving::with(['employee.institution']);

        // Apply same filters as index
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }
        if ($request->filled('institution_id')) {
            $query->whereHas('employee', function ($q) use ($request) {
                $q->where('institution_id', $request->institution_id);
            });
        }

        $filename = "savings_export_" . now()->format('Y_m_d_H_i_s') . ".csv";
        
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"$filename\"",
        ];

        $callback = function() use ($query) {
            $file = fopen('php://output', 'w');
            
            fputcsv($file, [
                'Employee ID', 'Employee Name', 'Institution', 
                'Expected Amount', 'Actual Amount', 'Due Date', 
                'Payment Date', 'Status', 'Admin Notes'
            ]);
            
            $query->chunk(100, function ($savings) use ($file) {
                foreach ($savings as $saving) {
                    fputcsv($file, [
                        $saving->employee->employee_id,
                        $saving->employee->full_name,
                        $saving->employee->institution->name,
                        $saving->expected_amount,
                        $saving->actual_amount,
                        $saving->due_date->format('Y-m-d'),
                        $saving->payment_date?->format('Y-m-d'),
                        $saving->status,
                        $saving->admin_notes,
                    ]);
                }
            });
            
            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
