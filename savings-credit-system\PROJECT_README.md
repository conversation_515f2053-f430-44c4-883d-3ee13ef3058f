# Employee Savings and Credit Management System

A comprehensive web-based financial management system for companies that manage savings and credit services for government employees from different institutions.

## 🎯 Purpose

This system tracks voluntary savings by employees and offers loans based on their savings and repayment history. The company centrally manages all employees and handles all approvals through manual payment verification.

## 👥 System Roles

- **Admin**: Full control over the platform—manages employees, verifies payments, approves or rejects loans, and generates reports.
- **Employee**: Can view expected monthly savings, upload payment proof, view savings history, request loans, and see repayment schedules.

## 🧩 Core Features

### 1. Institution & Employee Management
- Register and categorize government institutions
- Add/edit employee profiles: name, ID, institution, salary, employment date
- Calculate and assign expected monthly savings (fixed or % of salary)

### 2. Savings Management
- Each employee sees their expected monthly saving amount
- Employees upload payment proof (PDF/image) each month
- Admin reviews and either approves or disapproves the uploaded proof
- Only approved payments are added to the employee's savings balance
- Display full contribution history (status: approved / rejected / pending)

### 3. Credit (Loan) Management
- Employees can request loans through their dashboard
- Admin evaluates and approves or rejects loan applications
- Loan eligibility is based on savings (e.g., loan ≤ 3x savings)
- Admin sets loan terms: interest rate, duration, monthly installments
- Employee views loan details and repayment history
- Admin tracks repayments and optionally marks them as paid manually

### 4. Reports & Dashboards
- Admin Dashboard: KPIs like total savings, active savers, loan exposure, missed payments
- Employee Dashboard: personal savings status, loan status, due dates
- Generate downloadable reports (PDF/Excel) filtered by institution or employee

### 5. Notifications & Logs
- Notify employees when payment proof is approved, rejected, or pending for too long
- Notify employees of upcoming savings or repayment due dates
- Log all admin and employee activities: payment actions, loan approvals, account changes, etc.

## 💻 Technical Stack

- **Frontend**: Bootstrap 5 with Blade templates
- **Backend**: Laravel 12 (PHP Framework)
- **Database**: MySQL
- **Authentication**: Role-based access with Laravel's built-in authentication
- **File Uploads**: Store and serve uploaded payment proofs securely

## 🚀 Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd savings-credit-system
   ```

2. **Install dependencies**
   ```bash
   composer install
   npm install
   ```

3. **Environment setup**
   ```bash
   cp .env.example .env
   php artisan key:generate
   ```

4. **Database configuration**
   Update your `.env` file with your MySQL database credentials:
   ```
   DB_CONNECTION=mysql
   DB_HOST=127.0.0.1
   DB_PORT=3306
   DB_DATABASE=savings_credit_system
   DB_USERNAME=your_username
   DB_PASSWORD=your_password
   ```

5. **Run migrations and seed data**
   ```bash
   php artisan migrate
   php artisan db:seed
   ```

6. **Start the development server**
   ```bash
   php artisan serve
   ```

## 🔐 Default Login Credentials

### Admin Account
- **Email**: <EMAIL>
- **Password**: password

### Employee Accounts
- **Email**: <EMAIL>
- **Password**: password

- **Email**: <EMAIL>
- **Password**: password

## 📁 Project Structure

```
savings-credit-system/
├── app/
│   ├── Http/Controllers/
│   │   ├── Admin/           # Admin controllers
│   │   └── Employee/        # Employee controllers
│   ├── Models/              # Eloquent models
│   └── Http/Middleware/     # Custom middleware
├── database/
│   ├── migrations/          # Database migrations
│   └── seeders/            # Database seeders
├── resources/
│   └── views/              # Blade templates
└── routes/                 # Route definitions
```

## 🗄️ Database Schema

### Core Tables
- `institutions` - Government institutions
- `employees` - Employee profiles and salary info
- `users` - User accounts with role-based access
- `savings` - Monthly savings tracking
- `payment_proofs` - Uploaded payment evidence
- `loans` - Credit/loan management
- `loan_repayments` - Loan repayment tracking
- `activity_logs` - System activity logging

## 🔧 Key Features Implementation

### Role-Based Access Control
- Admin middleware for administrative functions
- Employee middleware for employee-specific features
- Route protection based on user roles

### Payment Verification Workflow
1. Employee uploads payment proof
2. Admin reviews uploaded documents
3. Admin approves/rejects with notes
4. System updates savings balance accordingly

### Loan Management System
- Automatic loan number generation
- Eligibility calculation based on savings
- Repayment schedule generation
- Overdue payment tracking

### Activity Logging
- Comprehensive audit trail
- User action tracking
- Model change logging
- IP address and user agent capture

## 🚢 Deployment

The system is Docker-ready and can be deployed on any VPS or cloud environment. For production deployment:

1. Set up a MySQL database
2. Configure environment variables
3. Run migrations
4. Set up file storage for payment proofs
5. Configure web server (Apache/Nginx)

## 📊 Reporting Features

- Dashboard analytics with KPIs
- Export functionality for savings and loan data
- Institution-wise performance reports
- Employee savings history
- Loan repayment tracking

## 🔒 Security Features

- Role-based access control
- CSRF protection
- SQL injection prevention
- Secure file upload handling
- Activity logging for audit trails

## 📞 Support

For technical support or questions about the system, contact your system administrator.

## 🎉 System Status

✅ **Completed Features:**
- Database schema and migrations
- User authentication with role-based access
- Admin and Employee dashboards
- Employee management system
- Savings management with approval workflow
- Basic reporting and analytics
- Responsive UI with Bootstrap 5

🚧 **In Progress:**
- Payment proof upload functionality
- Loan application and management
- Advanced reporting features
- Email notifications
- File export capabilities

📋 **Planned Features:**
- Mobile-responsive design improvements
- Advanced search and filtering
- Bulk operations
- API endpoints for mobile app
- Advanced analytics and charts
