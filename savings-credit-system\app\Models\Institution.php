<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Institution extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'code',
        'description',
        'address',
        'phone',
        'email',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * Get the employees for the institution.
     */
    public function employees(): HasMany
    {
        return $this->hasMany(Employee::class);
    }

    /**
     * Get active employees for the institution.
     */
    public function activeEmployees(): HasMany
    {
        return $this->hasMany(Employee::class)->where('is_active', true);
    }

    /**
     * Scope a query to only include active institutions.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Get the total savings for this institution.
     */
    public function getTotalSavingsAttribute()
    {
        return $this->employees()
            ->join('savings', 'employees.id', '=', 'savings.employee_id')
            ->where('savings.status', 'approved')
            ->sum('savings.actual_amount');
    }

    /**
     * Get the total number of active savers.
     */
    public function getActiveSaversCountAttribute()
    {
        return $this->employees()
            ->whereHas('savings', function ($query) {
                $query->where('status', 'approved')
                      ->where('due_date', '>=', now()->subMonths(3));
            })
            ->count();
    }
}
