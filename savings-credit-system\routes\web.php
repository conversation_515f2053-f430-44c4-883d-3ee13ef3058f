<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Admin\DashboardController as AdminDashboardController;
use App\Http\Controllers\Admin\EmployeeController;
use App\Http\Controllers\Admin\SavingsController;
use App\Http\Controllers\Employee\DashboardController as EmployeeDashboardController;

// Public routes
Route::get('/', function () {
    return redirect()->route('login');
});

// Authentication routes
require __DIR__.'/auth.php';

// Admin routes
Route::middleware(['auth', 'admin'])->prefix('admin')->name('admin.')->group(function () {
    Route::get('/dashboard', [AdminDashboardController::class, 'index'])->name('dashboard');

    // Employee management
    Route::resource('employees', EmployeeController::class);
    Route::post('employees/import', [EmployeeController::class, 'import'])->name('employees.import');

    // Savings management
    Route::resource('savings', SavingsController::class)->only(['index', 'show']);
    Route::post('savings/{saving}/approve', [SavingsController::class, 'approve'])->name('savings.approve');
    Route::post('savings/{saving}/reject', [SavingsController::class, 'reject'])->name('savings.reject');
    Route::post('savings/bulk-approve', [SavingsController::class, 'bulkApprove'])->name('savings.bulk-approve');
    Route::post('savings/generate-monthly', [SavingsController::class, 'generateMonthlySavings'])->name('savings.generate-monthly');
    Route::get('savings/export', [SavingsController::class, 'export'])->name('savings.export');
});

// Employee routes
Route::middleware(['auth', 'employee'])->prefix('employee')->name('employee.')->group(function () {
    Route::get('/dashboard', [EmployeeDashboardController::class, 'index'])->name('dashboard');
});

// Redirect after login based on role
Route::get('/home', function () {
    if (auth()->user()->isAdmin()) {
        return redirect()->route('admin.dashboard');
    }
    return redirect()->route('employee.dashboard');
})->middleware('auth')->name('home');
