@extends('layouts.app')

@section('content')
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">My Dashboard</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary">
                <i class="bi bi-download"></i> Export My Data
            </button>
        </div>
    </div>
</div>

<!-- Employee Info -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">Welcome, {{ $employee->full_name }}!</h5>
                <p class="card-text">
                    <strong>Employee ID:</strong> {{ $employee->employee_id }} |
                    <strong>Institution:</strong> {{ $employee->institution->name }} |
                    <strong>Expected Monthly Savings:</strong> ${{ number_format($employee->expected_monthly_savings, 2) }}
                </p>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stat-card">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Total Savings</div>
                        <div class="h5 mb-0 font-weight-bold">${{ number_format($totalSavings, 2) }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-piggy-bank fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stat-card success">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Current Balance</div>
                        <div class="h5 mb-0 font-weight-bold">${{ number_format($currentBalance, 2) }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-wallet2 fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stat-card warning">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Pending Savings</div>
                        <div class="h5 mb-0 font-weight-bold">{{ $pendingSavings }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-clock fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stat-card info">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Active Loans</div>
                        <div class="h5 mb-0 font-weight-bold">{{ $activeLoans }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="bi bi-cash-stack fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h6 class="m-0 font-weight-bold">
                    <i class="bi bi-upload"></i> Upload Payment Proof
                </h6>
            </div>
            <div class="card-body text-center">
                <p>Upload proof of your monthly savings payment</p>
                <button class="btn btn-primary">
                    <i class="bi bi-cloud-upload"></i> Upload Now
                </button>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-header bg-success text-white">
                <h6 class="m-0 font-weight-bold">
                    <i class="bi bi-cash"></i> Apply for Loan
                </h6>
            </div>
            <div class="card-body text-center">
                <p>Apply for a loan based on your savings</p>
                <button class="btn btn-success">
                    <i class="bi bi-plus-circle"></i> Apply Now
                </button>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h6 class="m-0 font-weight-bold">
                    <i class="bi bi-graph-up"></i> View History
                </h6>
            </div>
            <div class="card-body text-center">
                <p>View your complete savings and loan history</p>
                <button class="btn btn-info">
                    <i class="bi bi-eye"></i> View History
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Next Due Payment Alert -->
@if($nextDuePayment)
<div class="row mb-4">
    <div class="col-md-12">
        <div class="alert alert-warning">
            <h6><i class="bi bi-exclamation-triangle"></i> Upcoming Loan Payment</h6>
            <p class="mb-0">
                Your next loan payment of <strong>${{ number_format($nextDuePayment->expected_amount, 2) }}</strong> 
                is due on <strong>{{ $nextDuePayment->due_date->format('F j, Y') }}</strong>
                @if($nextDuePayment->due_date->isPast())
                    <span class="badge bg-danger">OVERDUE</span>
                @elseif($nextDuePayment->due_date->diffInDays() <= 7)
                    <span class="badge bg-warning">DUE SOON</span>
                @endif
            </p>
        </div>
    </div>
</div>
@endif

<!-- Recent Activities -->
<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold">
                    <i class="bi bi-clock-history"></i> Recent Savings
                </h6>
            </div>
            <div class="card-body">
                @if($recentSavings->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Due Date</th>
                                    <th>Amount</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($recentSavings as $saving)
                                <tr>
                                    <td>{{ $saving->due_date->format('M d, Y') }}</td>
                                    <td>${{ number_format($saving->expected_amount, 2) }}</td>
                                    <td>
                                        <span class="badge bg-{{ $saving->status === 'approved' ? 'success' : ($saving->status === 'rejected' ? 'danger' : 'warning') }}">
                                            {{ ucfirst($saving->status) }}
                                        </span>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <p class="text-muted">No savings records found.</p>
                @endif
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold">
                    <i class="bi bi-clock-history"></i> Recent Loans
                </h6>
            </div>
            <div class="card-body">
                @if($recentLoans->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Loan #</th>
                                    <th>Amount</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($recentLoans as $loan)
                                <tr>
                                    <td>{{ $loan->loan_number }}</td>
                                    <td>${{ number_format($loan->requested_amount, 2) }}</td>
                                    <td>
                                        <span class="badge bg-{{ $loan->status === 'approved' ? 'success' : ($loan->status === 'rejected' ? 'danger' : 'warning') }}">
                                            {{ ucfirst($loan->status) }}
                                        </span>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <p class="text-muted">No loan records found.</p>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection
