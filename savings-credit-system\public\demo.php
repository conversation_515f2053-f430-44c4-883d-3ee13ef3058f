<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Employee Savings & Credit Management System - Demo</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .stat-card.success {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        .stat-card.warning {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        .stat-card.info {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-md navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="bi bi-bank2"></i>
                Employee Savings & Credit System
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="#admin">Admin Demo</a>
                <a class="nav-link" href="#employee">Employee Demo</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- System Overview -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="alert alert-info">
                    <h4><i class="bi bi-info-circle"></i> System Demo</h4>
                    <p>This is a demonstration of the Employee Savings and Credit Management System. The full Laravel application requires proper composer dependencies to run.</p>
                    <p><strong>Features Implemented:</strong></p>
                    <ul>
                        <li>Complete database schema with 8 core tables</li>
                        <li>Role-based authentication (Admin/Employee)</li>
                        <li>Employee management system</li>
                        <li>Savings tracking and approval workflow</li>
                        <li>Loan management system</li>
                        <li>Activity logging and audit trails</li>
                        <li>Responsive Bootstrap UI</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Admin Dashboard Demo -->
        <div id="admin" class="row mb-5">
            <div class="col-12">
                <h2><i class="bi bi-speedometer2"></i> Admin Dashboard Demo</h2>
                
                <!-- Statistics Cards -->
                <div class="row mb-4">
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stat-card">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-uppercase mb-1">Total Employees</div>
                                        <div class="h5 mb-0 font-weight-bold">156</div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="bi bi-people fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stat-card success">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-uppercase mb-1">Total Savings</div>
                                        <div class="h5 mb-0 font-weight-bold">$1,245,680.50</div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="bi bi-piggy-bank fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stat-card warning">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-uppercase mb-1">Active Loans</div>
                                        <div class="h5 mb-0 font-weight-bold">$567,890.25</div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="bi bi-cash-stack fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stat-card info">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-uppercase mb-1">Institutions</div>
                                        <div class="h5 mb-0 font-weight-bold">12</div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="bi bi-building fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Pending Approvals -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header bg-warning text-dark">
                                <h6 class="m-0 font-weight-bold">
                                    <i class="bi bi-clock"></i> Pending Savings Approvals
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>Employee</th>
                                                <th>Amount</th>
                                                <th>Due Date</th>
                                                <th>Action</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>John Doe</td>
                                                <td>$250.00</td>
                                                <td>Dec 31, 2024</td>
                                                <td>
                                                    <button class="btn btn-success btn-sm">Approve</button>
                                                    <button class="btn btn-danger btn-sm">Reject</button>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>Jane Smith</td>
                                                <td>$225.00</td>
                                                <td>Dec 31, 2024</td>
                                                <td>
                                                    <button class="btn btn-success btn-sm">Approve</button>
                                                    <button class="btn btn-danger btn-sm">Reject</button>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header bg-info text-white">
                                <h6 class="m-0 font-weight-bold">
                                    <i class="bi bi-cash"></i> Pending Loan Applications
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>Employee</th>
                                                <th>Amount</th>
                                                <th>Purpose</th>
                                                <th>Action</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>Mike Johnson</td>
                                                <td>$5,000.00</td>
                                                <td>Home Repair</td>
                                                <td>
                                                    <button class="btn btn-success btn-sm">Approve</button>
                                                    <button class="btn btn-danger btn-sm">Reject</button>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Employee Dashboard Demo -->
        <div id="employee" class="row mb-5">
            <div class="col-12">
                <h2><i class="bi bi-person-circle"></i> Employee Dashboard Demo</h2>
                
                <!-- Employee Info -->
                <div class="row mb-4">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title">Welcome, John Doe!</h5>
                                <p class="card-text">
                                    <strong>Employee ID:</strong> EMP001 |
                                    <strong>Institution:</strong> Ministry of Health |
                                    <strong>Expected Monthly Savings:</strong> $250.00
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Employee Statistics -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card stat-card">
                            <div class="card-body text-center">
                                <h6>Total Savings</h6>
                                <h4>$3,750.00</h4>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stat-card success">
                            <div class="card-body text-center">
                                <h6>Available Balance</h6>
                                <h4>$3,750.00</h4>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stat-card warning">
                            <div class="card-body text-center">
                                <h6>Pending Savings</h6>
                                <h4>1</h4>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stat-card info">
                            <div class="card-body text-center">
                                <h6>Active Loans</h6>
                                <h4>0</h4>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h6 class="m-0"><i class="bi bi-upload"></i> Upload Payment Proof</h6>
                            </div>
                            <div class="card-body text-center">
                                <p>Upload proof of your monthly savings payment</p>
                                <button class="btn btn-primary">
                                    <i class="bi bi-cloud-upload"></i> Upload Now
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header bg-success text-white">
                                <h6 class="m-0"><i class="bi bi-cash"></i> Apply for Loan</h6>
                            </div>
                            <div class="card-body text-center">
                                <p>Apply for a loan based on your savings</p>
                                <button class="btn btn-success">
                                    <i class="bi bi-plus-circle"></i> Apply Now
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header bg-info text-white">
                                <h6 class="m-0"><i class="bi bi-graph-up"></i> View History</h6>
                            </div>
                            <div class="card-body text-center">
                                <p>View your complete savings and loan history</p>
                                <button class="btn btn-info">
                                    <i class="bi bi-eye"></i> View History
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- System Architecture -->
        <div class="row mb-5">
            <div class="col-12">
                <h2><i class="bi bi-diagram-3"></i> System Architecture</h2>
                <div class="card">
                    <div class="card-body">
                        <h5>Database Schema</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <ul>
                                    <li><strong>institutions</strong> - Government institutions</li>
                                    <li><strong>employees</strong> - Employee profiles and salary info</li>
                                    <li><strong>users</strong> - User accounts with role-based access</li>
                                    <li><strong>savings</strong> - Monthly savings tracking</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <ul>
                                    <li><strong>payment_proofs</strong> - Uploaded payment evidence</li>
                                    <li><strong>loans</strong> - Credit/loan management</li>
                                    <li><strong>loan_repayments</strong> - Loan repayment tracking</li>
                                    <li><strong>activity_logs</strong> - System activity logging</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <footer class="text-center py-4 border-top">
            <p class="text-muted">Employee Savings & Credit Management System - Built with Laravel + MySQL + Bootstrap</p>
        </footer>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
