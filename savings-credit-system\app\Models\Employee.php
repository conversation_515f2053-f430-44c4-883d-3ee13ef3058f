<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

class Employee extends Model
{
    use HasFactory;

    protected $fillable = [
        'employee_id',
        'first_name',
        'last_name',
        'email',
        'phone',
        'institution_id',
        'salary',
        'expected_monthly_savings',
        'savings_type',
        'savings_rate',
        'employment_date',
        'enrollment_date',
        'is_active',
    ];

    protected $casts = [
        'salary' => 'decimal:2',
        'expected_monthly_savings' => 'decimal:2',
        'savings_rate' => 'decimal:2',
        'employment_date' => 'date',
        'enrollment_date' => 'date',
        'is_active' => 'boolean',
    ];

    /**
     * Get the institution that owns the employee.
     */
    public function institution(): BelongsTo
    {
        return $this->belongsTo(Institution::class);
    }

    /**
     * Get the user account for the employee.
     */
    public function user(): HasOne
    {
        return $this->hasOne(User::class);
    }

    /**
     * Get the savings for the employee.
     */
    public function savings(): HasMany
    {
        return $this->hasMany(Saving::class);
    }

    /**
     * Get the loans for the employee.
     */
    public function loans(): HasMany
    {
        return $this->hasMany(Loan::class);
    }

    /**
     * Get the employee's full name.
     */
    public function getFullNameAttribute(): string
    {
        return $this->first_name . ' ' . $this->last_name;
    }

    /**
     * Calculate expected monthly savings based on type.
     */
    public function calculateExpectedSavings(): float
    {
        if ($this->savings_type === 'percentage') {
            return ($this->salary * $this->savings_rate) / 100;
        }
        
        return $this->expected_monthly_savings;
    }

    /**
     * Get total approved savings.
     */
    public function getTotalSavingsAttribute(): float
    {
        return $this->savings()
            ->where('status', 'approved')
            ->sum('actual_amount');
    }

    /**
     * Get current savings balance.
     */
    public function getCurrentSavingsBalanceAttribute(): float
    {
        $totalSavings = $this->total_savings;
        $totalLoans = $this->loans()
            ->whereIn('status', ['disbursed', 'completed'])
            ->sum('approved_amount');
        
        return $totalSavings - $totalLoans;
    }

    /**
     * Check if employee is eligible for loan.
     */
    public function isEligibleForLoan(float $amount): bool
    {
        $maxLoanAmount = $this->total_savings * 3; // 3x savings
        return $amount <= $maxLoanAmount && $this->is_active;
    }

    /**
     * Get pending savings count.
     */
    public function getPendingSavingsCountAttribute(): int
    {
        return $this->savings()->where('status', 'pending')->count();
    }

    /**
     * Get active loans.
     */
    public function getActiveLoansAttribute()
    {
        return $this->loans()->whereIn('status', ['approved', 'disbursed'])->get();
    }

    /**
     * Scope a query to only include active employees.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }
}
