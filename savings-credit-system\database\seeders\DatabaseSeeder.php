<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\Institution;
use App\Models\Employee;
use App\Models\Saving;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Create institutions
        $institutions = [
            ['name' => 'Ministry of Health', 'code' => 'MOH', 'description' => 'Government health ministry'],
            ['name' => 'Ministry of Education', 'code' => 'MOE', 'description' => 'Government education ministry'],
            ['name' => 'Ministry of Finance', 'code' => 'MOF', 'description' => 'Government finance ministry'],
        ];

        foreach ($institutions as $institutionData) {
            Institution::create($institutionData);
        }

        // Create admin user
        User::create([
            'name' => 'System Administrator',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'admin',
            'is_active' => true,
        ]);

        // Create sample employees
        $employees = [
            [
                'employee_id' => 'EMP001',
                'first_name' => 'John',
                'last_name' => 'Doe',
                'email' => '<EMAIL>',
                'institution_id' => 1,
                'salary' => 5000.00,
                'expected_monthly_savings' => 250.00,
                'savings_type' => 'fixed',
                'employment_date' => '2020-01-15',
                'enrollment_date' => '2024-01-01',
            ],
            [
                'employee_id' => 'EMP002',
                'first_name' => 'Jane',
                'last_name' => 'Smith',
                'email' => '<EMAIL>',
                'institution_id' => 2,
                'salary' => 4500.00,
                'expected_monthly_savings' => 225.00,
                'savings_type' => 'percentage',
                'savings_rate' => 5.00,
                'employment_date' => '2019-03-20',
                'enrollment_date' => '2024-01-01',
            ],
        ];

        foreach ($employees as $employeeData) {
            $employee = Employee::create($employeeData);

            // Create user account for employee
            User::create([
                'name' => $employee->full_name,
                'email' => $employee->email,
                'password' => Hash::make('password'),
                'role' => 'employee',
                'employee_id' => $employee->id,
                'is_active' => true,
            ]);

            // Create some sample savings records
            for ($i = 1; $i <= 3; $i++) {
                Saving::create([
                    'employee_id' => $employee->id,
                    'expected_amount' => $employee->expected_monthly_savings,
                    'actual_amount' => $employee->expected_monthly_savings,
                    'due_date' => now()->subMonths($i)->endOfMonth(),
                    'payment_date' => now()->subMonths($i)->endOfMonth(),
                    'status' => 'approved',
                    'approved_by' => 1,
                    'approved_at' => now()->subMonths($i)->endOfMonth(),
                ]);
            }

            // Create a pending saving for current month
            Saving::create([
                'employee_id' => $employee->id,
                'expected_amount' => $employee->expected_monthly_savings,
                'due_date' => now()->endOfMonth(),
                'status' => 'pending',
            ]);
        }
    }
}
