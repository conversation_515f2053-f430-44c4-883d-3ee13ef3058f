<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInit88970a0117c062eed55fa8728fc43833
{
    public static $prefixLengthsPsr4 = array (
        'D' => 
        array (
            'Database\\Seeders\\' => 17,
            'Database\\Factories\\' => 19,
        ),
        'A' => 
        array (
            'App\\' => 4,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'Database\\Seeders\\' => 
        array (
            0 => __DIR__ . '/../..' . '/database/seeders',
        ),
        'Database\\Factories\\' => 
        array (
            0 => __DIR__ . '/../..' . '/database/factories',
        ),
        'App\\' => 
        array (
            0 => __DIR__ . '/../..' . '/app',
        ),
    );

    public static $classMap = array (
        'App\\Http\\Controllers\\Admin\\DashboardController' => __DIR__ . '/../..' . '/app/Http/Controllers/Admin/DashboardController.php',
        'App\\Http\\Controllers\\Admin\\EmployeeController' => __DIR__ . '/../..' . '/app/Http/Controllers/Admin/EmployeeController.php',
        'App\\Http\\Controllers\\Admin\\SavingsController' => __DIR__ . '/../..' . '/app/Http/Controllers/Admin/SavingsController.php',
        'App\\Http\\Controllers\\Controller' => __DIR__ . '/../..' . '/app/Http/Controllers/Controller.php',
        'App\\Http\\Controllers\\Employee\\DashboardController' => __DIR__ . '/../..' . '/app/Http/Controllers/Employee/DashboardController.php',
        'App\\Http\\Middleware\\AdminMiddleware' => __DIR__ . '/../..' . '/app/Http/Middleware/AdminMiddleware.php',
        'App\\Http\\Middleware\\EmployeeMiddleware' => __DIR__ . '/../..' . '/app/Http/Middleware/EmployeeMiddleware.php',
        'App\\Models\\ActivityLog' => __DIR__ . '/../..' . '/app/Models/ActivityLog.php',
        'App\\Models\\Employee' => __DIR__ . '/../..' . '/app/Models/Employee.php',
        'App\\Models\\Institution' => __DIR__ . '/../..' . '/app/Models/Institution.php',
        'App\\Models\\Loan' => __DIR__ . '/../..' . '/app/Models/Loan.php',
        'App\\Models\\LoanRepayment' => __DIR__ . '/../..' . '/app/Models/LoanRepayment.php',
        'App\\Models\\PaymentProof' => __DIR__ . '/../..' . '/app/Models/PaymentProof.php',
        'App\\Models\\Saving' => __DIR__ . '/../..' . '/app/Models/Saving.php',
        'App\\Models\\User' => __DIR__ . '/../..' . '/app/Models/User.php',
        'App\\Providers\\AppServiceProvider' => __DIR__ . '/../..' . '/app/Providers/AppServiceProvider.php',
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
        'Database\\Factories\\UserFactory' => __DIR__ . '/../..' . '/database/factories/UserFactory.php',
        'Database\\Seeders\\DatabaseSeeder' => __DIR__ . '/../..' . '/database/seeders/DatabaseSeeder.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInit88970a0117c062eed55fa8728fc43833::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInit88970a0117c062eed55fa8728fc43833::$prefixDirsPsr4;
            $loader->classMap = ComposerStaticInit88970a0117c062eed55fa8728fc43833::$classMap;

        }, null, ClassLoader::class);
    }
}
