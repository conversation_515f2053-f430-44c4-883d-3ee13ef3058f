<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class LoanRepayment extends Model
{
    use HasFactory;

    protected $fillable = [
        'loan_id',
        'installment_number',
        'expected_amount',
        'paid_amount',
        'due_date',
        'payment_date',
        'status',
        'penalty_amount',
        'notes',
        'recorded_by',
    ];

    protected $casts = [
        'expected_amount' => 'decimal:2',
        'paid_amount' => 'decimal:2',
        'penalty_amount' => 'decimal:2',
        'due_date' => 'date',
        'payment_date' => 'date',
    ];

    /**
     * Get the loan that owns the repayment.
     */
    public function loan(): BelongsTo
    {
        return $this->belongsTo(Loan::class);
    }

    /**
     * Get the user who recorded the payment.
     */
    public function recordedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'recorded_by');
    }

    /**
     * Check if payment is overdue.
     */
    public function getIsOverdueAttribute(): bool
    {
        return $this->status === 'pending' && $this->due_date < now();
    }

    /**
     * Get days overdue.
     */
    public function getDaysOverdueAttribute(): int
    {
        if (!$this->is_overdue) {
            return 0;
        }
        
        return now()->diffInDays($this->due_date);
    }

    /**
     * Calculate penalty for overdue payment.
     */
    public function calculatePenalty(float $penaltyRate = 0.05): float
    {
        if (!$this->is_overdue) {
            return 0;
        }

        $daysOverdue = $this->days_overdue;
        $monthsOverdue = ceil($daysOverdue / 30);
        
        return $this->expected_amount * $penaltyRate * $monthsOverdue;
    }

    /**
     * Record payment.
     */
    public function recordPayment(float $amount, User $recordedBy, ?string $notes = null): bool
    {
        $penalty = $this->calculatePenalty();
        $totalExpected = $this->expected_amount + $penalty;

        $status = 'paid';
        if ($amount < $totalExpected) {
            $status = 'partial';
        }

        $this->update([
            'paid_amount' => $amount,
            'payment_date' => now(),
            'status' => $status,
            'penalty_amount' => $penalty,
            'notes' => $notes,
            'recorded_by' => $recordedBy->id,
        ]);

        // Update loan status if all payments are complete
        $this->updateLoanStatus();

        return true;
    }

    /**
     * Update loan status based on repayment status.
     */
    private function updateLoanStatus(): void
    {
        $loan = $this->loan;
        $totalRepayments = $loan->repayments()->count();
        $paidRepayments = $loan->repayments()->where('status', 'paid')->count();

        if ($paidRepayments === $totalRepayments) {
            $loan->update(['status' => 'completed']);
        }
    }

    /**
     * Scope queries for different statuses.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopePaid($query)
    {
        return $query->where('status', 'paid');
    }

    public function scopeOverdue($query)
    {
        return $query->where('status', 'pending')
                    ->where('due_date', '<', now());
    }

    public function scopePartial($query)
    {
        return $query->where('status', 'partial');
    }
}
