<?php

// autoload_classmap.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    'App\\Http\\Controllers\\Admin\\DashboardController' => $baseDir . '/app/Http/Controllers/Admin/DashboardController.php',
    'App\\Http\\Controllers\\Admin\\EmployeeController' => $baseDir . '/app/Http/Controllers/Admin/EmployeeController.php',
    'App\\Http\\Controllers\\Admin\\SavingsController' => $baseDir . '/app/Http/Controllers/Admin/SavingsController.php',
    'App\\Http\\Controllers\\Controller' => $baseDir . '/app/Http/Controllers/Controller.php',
    'App\\Http\\Controllers\\Employee\\DashboardController' => $baseDir . '/app/Http/Controllers/Employee/DashboardController.php',
    'App\\Http\\Middleware\\AdminMiddleware' => $baseDir . '/app/Http/Middleware/AdminMiddleware.php',
    'App\\Http\\Middleware\\EmployeeMiddleware' => $baseDir . '/app/Http/Middleware/EmployeeMiddleware.php',
    'App\\Models\\ActivityLog' => $baseDir . '/app/Models/ActivityLog.php',
    'App\\Models\\Employee' => $baseDir . '/app/Models/Employee.php',
    'App\\Models\\Institution' => $baseDir . '/app/Models/Institution.php',
    'App\\Models\\Loan' => $baseDir . '/app/Models/Loan.php',
    'App\\Models\\LoanRepayment' => $baseDir . '/app/Models/LoanRepayment.php',
    'App\\Models\\PaymentProof' => $baseDir . '/app/Models/PaymentProof.php',
    'App\\Models\\Saving' => $baseDir . '/app/Models/Saving.php',
    'App\\Models\\User' => $baseDir . '/app/Models/User.php',
    'App\\Providers\\AppServiceProvider' => $baseDir . '/app/Providers/AppServiceProvider.php',
    'Composer\\InstalledVersions' => $vendorDir . '/composer/InstalledVersions.php',
    'Database\\Factories\\UserFactory' => $baseDir . '/database/factories/UserFactory.php',
    'Database\\Seeders\\DatabaseSeeder' => $baseDir . '/database/seeders/DatabaseSeeder.php',
);
