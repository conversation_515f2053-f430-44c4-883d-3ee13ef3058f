<?php

namespace App\Http\Controllers\Employee;

use App\Http\Controllers\Controller;
use App\Models\Saving;
use App\Models\Loan;
use Illuminate\View\View;

class DashboardController extends Controller
{
    /**
     * Display the employee dashboard.
     */
    public function index(): View
    {
        $employee = auth()->user()->employee;

        // Personal statistics
        $totalSavings = $employee->total_savings;
        $currentBalance = $employee->current_savings_balance;
        $pendingSavings = $employee->pending_savings_count;
        $activeLoans = $employee->active_loans->count();

        // Recent savings
        $recentSavings = $employee->savings()
            ->latest()
            ->take(5)
            ->get();

        // Recent loans
        $recentLoans = $employee->loans()
            ->latest()
            ->take(3)
            ->get();

        // Next due payment
        $nextDuePayment = null;
        if ($activeLoans > 0) {
            $nextDuePayment = $employee->loans()
                ->whereIn('status', ['disbursed'])
                ->with(['repayments' => function ($query) {
                    $query->where('status', 'pending')
                          ->orderBy('due_date');
                }])
                ->get()
                ->flatMap->repayments
                ->first();
        }

        // Monthly savings trend (last 6 months)
        $savingsTrend = [];
        for ($i = 5; $i >= 0; $i--) {
            $date = now()->subMonths($i);
            $month = $date->format('M Y');
            $amount = $employee->savings()
                ->where('status', 'approved')
                ->whereYear('payment_date', $date->year)
                ->whereMonth('payment_date', $date->month)
                ->sum('actual_amount');
            $savingsTrend[$month] = $amount;
        }

        return view('employee.dashboard', compact(
            'employee',
            'totalSavings',
            'currentBalance',
            'pendingSavings',
            'activeLoans',
            'recentSavings',
            'recentLoans',
            'nextDuePayment',
            'savingsTrend'
        ));
    }
}
