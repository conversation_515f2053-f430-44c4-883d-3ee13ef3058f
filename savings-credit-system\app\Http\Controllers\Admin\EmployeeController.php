<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Employee;
use App\Models\Institution;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;

class EmployeeController extends Controller
{
    /**
     * Display a listing of employees.
     */
    public function index(Request $request): View
    {
        $query = Employee::with('institution');

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('first_name', 'like', "%{$search}%")
                  ->orWhere('last_name', 'like', "%{$search}%")
                  ->orWhere('employee_id', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        // Filter by institution
        if ($request->filled('institution_id')) {
            $query->where('institution_id', $request->institution_id);
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('is_active', $request->status === 'active');
        }

        $employees = $query->paginate(15);
        $institutions = Institution::active()->get();

        return view('admin.employees.index', compact('employees', 'institutions'));
    }

    /**
     * Show the form for creating a new employee.
     */
    public function create(): View
    {
        $institutions = Institution::active()->get();
        return view('admin.employees.create', compact('institutions'));
    }

    /**
     * Store a newly created employee.
     */
    public function store(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'employee_id' => 'required|string|unique:employees',
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|email|unique:employees',
            'phone' => 'nullable|string|max:20',
            'institution_id' => 'required|exists:institutions,id',
            'salary' => 'required|numeric|min:0',
            'savings_type' => 'required|in:fixed,percentage',
            'expected_monthly_savings' => 'required_if:savings_type,fixed|nullable|numeric|min:0',
            'savings_rate' => 'required_if:savings_type,percentage|nullable|numeric|min:0|max:100',
            'employment_date' => 'required|date',
            'enrollment_date' => 'required|date',
        ]);

        // Calculate expected monthly savings
        if ($validated['savings_type'] === 'percentage') {
            $validated['expected_monthly_savings'] = ($validated['salary'] * $validated['savings_rate']) / 100;
        }

        $employee = Employee::create($validated);

        // Create user account for employee
        User::create([
            'name' => $employee->full_name,
            'email' => $employee->email,
            'password' => Hash::make('password123'), // Default password
            'role' => 'employee',
            'employee_id' => $employee->id,
        ]);

        return redirect()->route('admin.employees.index')
            ->with('success', 'Employee created successfully. Default password is: password123');
    }

    /**
     * Display the specified employee.
     */
    public function show(Employee $employee): View
    {
        $employee->load(['institution', 'savings', 'loans']);
        return view('admin.employees.show', compact('employee'));
    }

    /**
     * Show the form for editing the specified employee.
     */
    public function edit(Employee $employee): View
    {
        $institutions = Institution::active()->get();
        return view('admin.employees.edit', compact('employee', 'institutions'));
    }

    /**
     * Update the specified employee.
     */
    public function update(Request $request, Employee $employee): RedirectResponse
    {
        $validated = $request->validate([
            'employee_id' => 'required|string|unique:employees,employee_id,' . $employee->id,
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|email|unique:employees,email,' . $employee->id,
            'phone' => 'nullable|string|max:20',
            'institution_id' => 'required|exists:institutions,id',
            'salary' => 'required|numeric|min:0',
            'savings_type' => 'required|in:fixed,percentage',
            'expected_monthly_savings' => 'required_if:savings_type,fixed|nullable|numeric|min:0',
            'savings_rate' => 'required_if:savings_type,percentage|nullable|numeric|min:0|max:100',
            'employment_date' => 'required|date',
            'enrollment_date' => 'required|date',
            'is_active' => 'boolean',
        ]);

        // Calculate expected monthly savings
        if ($validated['savings_type'] === 'percentage') {
            $validated['expected_monthly_savings'] = ($validated['salary'] * $validated['savings_rate']) / 100;
        }

        $employee->update($validated);

        // Update user account if email changed
        if ($employee->user && $employee->user->email !== $validated['email']) {
            $employee->user->update([
                'email' => $validated['email'],
                'name' => $employee->full_name,
            ]);
        }

        return redirect()->route('admin.employees.index')
            ->with('success', 'Employee updated successfully.');
    }

    /**
     * Remove the specified employee.
     */
    public function destroy(Employee $employee): RedirectResponse
    {
        // Soft delete by deactivating
        $employee->update(['is_active' => false]);
        
        // Also deactivate user account
        if ($employee->user) {
            $employee->user->update(['is_active' => false]);
        }

        return redirect()->route('admin.employees.index')
            ->with('success', 'Employee deactivated successfully.');
    }

    /**
     * Bulk import employees from CSV.
     */
    public function import(Request $request): RedirectResponse
    {
        $request->validate([
            'csv_file' => 'required|file|mimes:csv,txt',
        ]);

        $file = $request->file('csv_file');
        $csvData = array_map('str_getcsv', file($file->getRealPath()));
        $header = array_shift($csvData);

        $imported = 0;
        $errors = [];

        foreach ($csvData as $row) {
            try {
                $data = array_combine($header, $row);
                
                // Validate and create employee
                $employee = Employee::create([
                    'employee_id' => $data['employee_id'],
                    'first_name' => $data['first_name'],
                    'last_name' => $data['last_name'],
                    'email' => $data['email'],
                    'phone' => $data['phone'] ?? null,
                    'institution_id' => $data['institution_id'],
                    'salary' => $data['salary'],
                    'expected_monthly_savings' => $data['expected_monthly_savings'],
                    'savings_type' => $data['savings_type'] ?? 'fixed',
                    'employment_date' => $data['employment_date'],
                    'enrollment_date' => $data['enrollment_date'],
                ]);

                // Create user account
                User::create([
                    'name' => $employee->full_name,
                    'email' => $employee->email,
                    'password' => Hash::make('password123'),
                    'role' => 'employee',
                    'employee_id' => $employee->id,
                ]);

                $imported++;
            } catch (\Exception $e) {
                $errors[] = "Row " . ($imported + count($errors) + 2) . ": " . $e->getMessage();
            }
        }

        $message = "Imported {$imported} employees successfully.";
        if (!empty($errors)) {
            $message .= " Errors: " . implode(', ', $errors);
        }

        return redirect()->route('admin.employees.index')->with('success', $message);
    }
}
